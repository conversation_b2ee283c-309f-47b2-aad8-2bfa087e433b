import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Paper,
  Card,
  CardContent,
  CardActions,
  Grid2 as Grid,
  Chip,
  Divider,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  CreditCard,
  Security,
  CheckCircle,
  Star,
  ExpandMore,
  Lock,
  Payment,
  Help
} from '@mui/icons-material';
import { loadStripe } from '@stripe/stripe-js';

const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || 'pk_test_YOUR_KEY');

interface CreditPack {
  id: string;
  name: string;
  credits: number;
  price: number;
  originalPrice?: number;
  isPopular?: boolean;
  bonusText?: string;
  stripeProductId: string;
}

interface AddCreditsPageProps {
  userCredits: number;
  onCreditsUpdated: () => void;
}

const AddCreditsPage: React.FC<AddCreditsPageProps> = ({ userCredits, onCreditsUpdated }) => {
  const [loading, setLoading] = useState(false);
  const [selectedPack, setSelectedPack] = useState<CreditPack | null>(null);
  const [showCheckout, setShowCheckout] = useState(false);

  const creditPacks: CreditPack[] = [
    {
      id: 'starter',
      name: 'Starter Pack',
      credits: 500,
      price: 4.99,
      stripeProductId: 'price_starter_pack'
    },
    {
      id: 'best-value',
      name: 'Best Value Pack',
      credits: 1200,
      price: 9.99,
      originalPrice: 11.99,
      isPopular: true,
      bonusText: '20% Bonus Credits!',
      stripeProductId: 'price_best_value_pack'
    },
    {
      id: 'premium',
      name: 'Premium Pack',
      credits: 2500,
      price: 19.99,
      originalPrice: 24.99,
      bonusText: '25% Bonus Credits!',
      stripeProductId: 'price_premium_pack'
    },
    {
      id: 'enterprise',
      name: 'Enterprise Pack',
      credits: 5000,
      price: 34.99,
      originalPrice: 49.99,
      bonusText: '43% Bonus Credits!',
      stripeProductId: 'price_enterprise_pack'
    }
  ];

  const handlePurchase = async (pack: CreditPack) => {
    setLoading(true);
    setSelectedPack(pack);

    try {
      const token = localStorage.getItem('jwt');
      const response = await fetch('/api/payments/create-credit-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          creditPackId: pack.id,
          priceId: pack.stripeProductId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create checkout session');
      }

      const { sessionId } = await response.json();
      const stripe = await stripePromise;

      if (stripe) {
        const { error } = await stripe.redirectToCheckout({ sessionId });
        if (error) {
          console.error('Stripe error:', error);
        }
      }
    } catch (error) {
      console.error('Purchase error:', error);
    } finally {
      setLoading(false);
      setSelectedPack(null);
    }
  };

  const formatPrice = (price: number) => `$${price.toFixed(2)}`;

  const faqItems = [
    {
      question: "Do my credits expire?",
      answer: "No, your credits never expire. Once purchased, they remain in your account until you use them."
    },
    {
      question: "What payment methods do you accept?",
      answer: "We accept all major credit cards (Visa, Mastercard, American Express, Discover) via Stripe's secure payment processing."
    },
    {
      question: "Is my payment information secure?",
      answer: "Yes, all transactions are processed by Stripe, a PCI-compliant payment processor. We never store your credit card details on our servers."
    }
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ textAlign: 'center', mb: 6 }}>
        <Typography
          variant="h3"
          component="h1"
          gutterBottom
          sx={{
            fontWeight: 400,
            fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
            color: 'text.primary'
          }}
        >
          Add Credits to Your Account
        </Typography>
        <Typography
          variant="h6"
          color="text.secondary"
          sx={{
            mb: 2,
            fontWeight: 500,
            fontSize: { xs: '1.1rem', sm: '1.25rem' }
          }}
        >
          Your current balance: <Box component="span" sx={{ color: 'primary.main', fontWeight: 600 }}>
            {userCredits.toLocaleString()} Credits
          </Box>
        </Typography>
        <Typography
          variant="body1"
          color="text.secondary"
          sx={{
            maxWidth: 600,
            mx: 'auto',
            lineHeight: 1.6,
            fontSize: '1rem'
          }}
        >
          Purchase credits to compress your photos and videos. Credits never expire and can be used anytime.
        </Typography>
      </Box>

      {/* Credit Packs */}
      <Grid container spacing={3} sx={{ mb: 6 }}>
        {creditPacks.map((pack) => (
          <Grid xs={12} sm={6} md={3} key={pack.id}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                position: 'relative',
                border: pack.isPopular ? 2 : 1,
                borderColor: pack.isPopular ? 'primary.main' : 'divider',
                borderRadius: 3,
                overflow: 'visible',
                backgroundColor: pack.isPopular ? 'primary.50' : 'background.paper',
                '&:hover': {
                  boxShadow: pack.isPopular ? 8 : 6,
                  transform: 'translateY(-4px)',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  borderColor: pack.isPopular ? 'primary.main' : 'primary.light'
                },
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
              }}
            >
              {pack.isPopular && (
                <Chip
                  label="Most Popular"
                  color="primary"
                  icon={<Star />}
                  sx={{
                    position: 'absolute',
                    top: -12,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    fontWeight: 600,
                    zIndex: 1,
                    fontSize: '0.75rem',
                    height: 24,
                    '& .MuiChip-icon': {
                      fontSize: 16
                    },
                    boxShadow: 2
                  }}
                />
              )}
              
              <CardContent sx={{ flexGrow: 1, textAlign: 'center', pt: pack.isPopular ? 4 : 3 }}>
                <Typography variant="h6" component="h3" gutterBottom sx={{ fontWeight: 600 }}>
                  {pack.name}
                </Typography>
                
                <Typography variant="h4" component="div" sx={{ fontWeight: 700, color: 'primary.main', mb: 1 }}>
                  {pack.credits.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Credits
                </Typography>

                <Box sx={{ my: 2 }}>
                  {pack.originalPrice && (
                    <Typography
                      variant="body2"
                      sx={{
                        textDecoration: 'line-through',
                        color: 'text.disabled',
                        mb: 0.5
                      }}
                    >
                      {formatPrice(pack.originalPrice)}
                    </Typography>
                  )}
                  <Typography variant="h5" component="div" sx={{ fontWeight: 600 }}>
                    {formatPrice(pack.price)}
                  </Typography>
                </Box>

                {pack.bonusText && (
                  <Chip
                    label={pack.bonusText}
                    color="success"
                    variant="outlined"
                    size="small"
                    sx={{ mb: 2 }}
                  />
                )}
              </CardContent>

              <CardActions sx={{ p: 2 }}>
                <Button
                  variant={pack.isPopular ? "contained" : "outlined"}
                  color="primary"
                  fullWidth
                  size="large"
                  onClick={() => handlePurchase(pack)}
                  disabled={loading && selectedPack?.id === pack.id}
                  sx={{
                    textTransform: 'none',
                    fontWeight: 600,
                    borderRadius: 2
                  }}
                >
                  {loading && selectedPack?.id === pack.id ? (
                    <CircularProgress size={24} />
                  ) : (
                    'Purchase'
                  )}
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Trust Indicators */}
      <Paper sx={{ p: 3, mb: 4, bgcolor: 'grey.50' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 4, flexWrap: 'wrap' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Lock sx={{ color: 'success.main' }} />
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              Secure SSL Checkout
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Payment sx={{ color: 'primary.main' }} />
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              Powered by Stripe
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CheckCircle sx={{ color: 'success.main' }} />
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              Instant Credit Delivery
            </Typography>
          </Box>
        </Box>
      </Paper>

      {/* FAQ Section */}
      <Box sx={{ maxWidth: 800, mx: 'auto' }}>
        <Typography variant="h5" component="h2" gutterBottom sx={{ textAlign: 'center', mb: 3, fontWeight: 600 }}>
          Frequently Asked Questions
        </Typography>
        
        {faqItems.map((item, index) => (
          <Accordion key={index} sx={{ mb: 1 }}>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                {item.question}
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" color="text.secondary">
                {item.answer}
              </Typography>
            </AccordionDetails>
          </Accordion>
        ))}
      </Box>
    </Container>
  );
};

export default AddCreditsPage;
